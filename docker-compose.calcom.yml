version: '3.8'

services:
  calcom-app:
    image: calcom/cal.com:latest
    container_name: calcom-app
    restart: always
    environment:
      # Database Configuration (Using your Neon database)
      DATABASE_URL: ${DATABASE_URL}
      DIRECT_URL: ${DIRECT_URL}
      
      # Application Configuration
      NEXTAUTH_SECRET: ${CALCOM_NEXTAUTH_SECRET}
      CALENDSO_ENCRYPTION_KEY: ${CALCOM_ENCRYPTION_KEY}
      
      # Public URLs (Cal.com runs on port 3002 to avoid conflict)
      NEXT_PUBLIC_WEBAPP_URL: http://localhost:3002
      NEXTAUTH_URL: http://localhost:3002

      # API Configuration
      API_V2_URL: http://localhost:3002/api/v2
      
      # Daily.co Integration (reuse existing credentials)
      DAILY_API_KEY: ${DAILY_API_KEY}
      DAILY_SCALE_PLAN: ${DAILY_SCALE_PLAN:-false}
      
      # Email Configuration (optional)
      EMAIL_FROM: ${CALCOM_EMAIL_FROM:-<EMAIL>}
      EMAIL_SERVER_HOST: ${CALCOM_EMAIL_HOST}
      EMAIL_SERVER_PORT: ${CALCOM_EMAIL_PORT:-587}
      EMAIL_SERVER_USER: ${CALCOM_EMAIL_USER}
      EMAIL_SERVER_PASSWORD: ${CALCOM_EMAIL_PASSWORD}
      
      # Disable telemetry for self-hosted
      NEXT_PUBLIC_TELEMETRY_KEY: ""
      CALCOM_TELEMETRY_DISABLED: "1"
      
      # License (for self-hosted)
      NEXT_PUBLIC_LICENSE_CONSENT: "agree"
      
      # Timezone
      TZ: ${TIMEZONE:-UTC}
      
    ports:
      - "3002:3000"
    volumes:
      - calcom_app_data:/app/.next
    networks:
      - calcom-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  calcom_app_data:
    driver: local

networks:
  calcom-network:
    driver: bridge
