# .env file
.env

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage
/test-results/
playwright/videos
playwright/screenshots
playwright/artifacts
playwright/results
playwright/reports/*

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example

# vercel
.vercel

# Webstorm
.idea

### VisualStudioCode template
.vscode/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Typescript
tsconfig.tsbuildinfo

# turbo
.turbo

# Prisma-Zod
packages/prisma/zod/*.ts

# Builds
dist

# Linting
lint-results

# Yarn
yarn-error.log

.turbo
.next
.husky
.vscode
.env