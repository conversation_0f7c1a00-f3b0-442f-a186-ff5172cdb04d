# https://playwright.dev/docs/test-sharding#merging-reports-from-multiple-shards
on:
  workflow_call:
jobs:
  merge-reports:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - uses: ./.github/actions/yarn-playwright-install
      - name: Download blob reports from GitHub Actions Artifacts
        uses: actions/download-artifact@v4
        with:
          path: all-blob-reports
          pattern: blob-report-*
          merge-multiple: true
      - name: Merge into HTML Report
        run: yarn playwright merge-reports --reporter html ./all-blob-reports
      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        with:
          name: html-report--attempt-${{ github.run_number }}-${{ github.run_attempt }}
          path: playwright-report
          retention-days: 14
