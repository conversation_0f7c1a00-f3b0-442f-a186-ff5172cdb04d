name: Atoms production Build

on:
  workflow_call:

jobs:
  build:
    name: Build Atoms
    permissions:
      contents: read
    runs-on: buildjet-4vcpu-ubuntu-2204
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - name: Cache atoms production build
        uses: buildjet/cache@v4
        id: cache-atoms-build
        env:
          cache-name: atoms-build
          key-1: ${{ hashFiles('yarn.lock') }}
          key-2: ${{ hashFiles('packages/platform/atoms/**.[jt]s', 'packages/platform/atoms/**.[jt]sx', '!**/node_modules') }}
          key-3: ${{ github.event.pull_request.number || github.ref }}
          # Ensures production-build.yml will always be fresh
          key-4: ${{ github.sha }}
        with:
          path: |
            **/dist/**
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ env.key-1 }}-${{ env.key-2 }}-${{ env.key-3 }}-${{ env.key-4 }}
      - name: Log Cache Hit
        if: steps.cache-atoms-build.outputs.cache-hit == 'true'
        run: echo "Cache hit for Atoms build. Skipping build."
      - name: Run build
        if: steps.cache-atoms-build.outputs.cache-hit != 'true'
        run: |
          export NODE_OPTIONS="--max_old_space_size=8192"
          rm -rf packages/platform/atoms/node_modules
          yarn install
          yarn workspace @calcom/atoms run build
        shell: bash
