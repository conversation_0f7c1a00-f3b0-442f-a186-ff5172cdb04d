---
name: Feature request
about: Suggest a feature or idea
title: ""
labels: ["✨ feature", "🚨 needs approval"]
assignees: ""
---

### Is your proposal related to a problem?

<!--
  Provide a clear and concise description of what the problem is.
  For example, "I'm always frustrated when..."
-->

(Write your answer here.)

### Describe the solution you'd like

<!--
  Provide a clear and concise description of what you want to happen.
-->

(Describe your proposed solution here.)

### Describe alternatives you've considered

<!--
  Let us know about other solutions you've tried or researched.
-->

(Write your answer here.)

### Additional context

<!--
  Is there anything else you can add about the proposal?
  You might want to link to related issues here, if you haven't already.
-->

(Write your answer here.)

### Requirement/Document

<!--
  Is there any type of document that could support that feature?
-->

(Share it here.)

---

##### House rules

- If this issue has a `🚨 needs approval` label, don't start coding yet. Wait until a core member approves feature request by removing this label, then you can start coding.
  - For clarity: Non-core member issues automatically get the `🚨 needs approval` label.
  - Your feature ideas are invaluable to us! However, they undergo review to ensure alignment with the product's direction.
  - Follow Best Practices lined out in our [Contributor Docs](https://github.com/calcom/cal.com/blob/main/CONTRIBUTING.md)
