const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('Creating test user...');
    
    const password = await bcrypt.hash('test123!', 12);
    
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        password: password,
        role: 'PATIENT',
        phone: '+**********',
        emailVerified: new Date(),
      },
    });

    console.log('✅ Test user created:', user.email);
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: test123!');
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
