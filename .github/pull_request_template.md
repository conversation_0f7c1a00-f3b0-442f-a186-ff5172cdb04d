# Pull Request

## 📋 Description

<!-- Provide a brief description of the changes in this PR -->

### What does this PR do?
- [ ] Adds new feature
- [ ] Fixes bug
- [ ] Improves performance
- [ ] Refactors code
- [ ] Updates documentation
- [ ] Updates dependencies
- [ ] Other: _______________

### Related Issues
<!-- Link to related issues using "Fixes #123" or "Closes #123" -->
- Fixes #
- Related to #

## 🔄 Type of Change

- [ ] **feat**: A new feature
- [ ] **fix**: A bug fix
- [ ] **docs**: Documentation only changes
- [ ] **style**: Changes that do not affect the meaning of the code
- [ ] **refactor**: A code change that neither fixes a bug nor adds a feature
- [ ] **perf**: A code change that improves performance
- [ ] **test**: Adding missing tests or correcting existing tests
- [ ] **chore**: Changes to the build process or auxiliary tools
- [ ] **ci**: Changes to CI configuration files and scripts
- [ ] **build**: Changes that affect the build system or external dependencies

## 🧪 Testing

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] E2E tests added/updated
- [ ] Manual testing completed

### Test Results
<!-- Describe the testing you've done -->
- [ ] All existing tests pass
- [ ] New tests pass
- [ ] No test coverage regression

### Manual Testing Steps
<!-- Describe how to manually test this change -->
1. 
2. 
3. 

## 📱 Frontend Changes (if applicable)

### UI/UX Changes
- [ ] New components added
- [ ] Existing components modified
- [ ] Styling changes
- [ ] Responsive design verified
- [ ] Accessibility considerations addressed

### Browser Testing
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers

### Screenshots/Videos
<!-- Add screenshots or videos showing the changes -->

## 🔧 Backend Changes (if applicable)

### API Changes
- [ ] New endpoints added
- [ ] Existing endpoints modified
- [ ] Breaking changes (describe below)
- [ ] Database schema changes
- [ ] Migration scripts included

### Database Changes
- [ ] New tables/columns added
- [ ] Existing schema modified
- [ ] Data migration required
- [ ] Indexes added/modified

### Performance Impact
- [ ] No performance impact
- [ ] Performance improved
- [ ] Performance may be affected (explain below)

## 🔒 Security Considerations

- [ ] No security implications
- [ ] Security review required
- [ ] Authentication/authorization changes
- [ ] Input validation added/updated
- [ ] SQL injection prevention verified
- [ ] XSS prevention verified

### Security Checklist
- [ ] No hardcoded secrets or credentials
- [ ] Sensitive data properly encrypted
- [ ] Proper error handling (no sensitive info in errors)
- [ ] Rate limiting considered
- [ ] CORS properly configured

## 📚 Documentation

- [ ] Code comments added/updated
- [ ] README updated
- [ ] API documentation updated
- [ ] Changelog updated
- [ ] Migration guide provided (if breaking changes)

## ⚡ Performance

### Bundle Size Impact
- [ ] No impact on bundle size
- [ ] Bundle size increased (justified below)
- [ ] Bundle size decreased

### Performance Metrics
- [ ] Lighthouse scores maintained/improved
- [ ] Core Web Vitals not negatively impacted
- [ ] Database query performance verified
- [ ] API response times verified

## 🔄 Breaking Changes

<!-- List any breaking changes and migration steps -->
- [ ] No breaking changes
- [ ] Breaking changes (describe below)

### Migration Steps
<!-- If there are breaking changes, provide migration steps -->

## 📋 Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is self-documenting or well-commented
- [ ] No console.log statements left in code
- [ ] No TODO/FIXME comments (or justified)
- [ ] Error handling implemented
- [ ] Edge cases considered

### Dependencies
- [ ] No new dependencies added
- [ ] New dependencies justified and documented
- [ ] Dependencies updated to latest secure versions
- [ ] No unused dependencies

### Git
- [ ] Commit messages follow conventional format
- [ ] Branch is up to date with target branch
- [ ] No merge conflicts
- [ ] Commits are atomic and well-described

### Deployment
- [ ] Environment variables documented
- [ ] Configuration changes documented
- [ ] Deployment steps documented (if special steps required)
- [ ] Rollback plan considered

## 🎯 Reviewer Focus Areas

<!-- Guide reviewers on what to focus on -->
Please pay special attention to:
- [ ] Security implications
- [ ] Performance impact
- [ ] Error handling
- [ ] Edge cases
- [ ] User experience
- [ ] Code maintainability

## 📝 Additional Notes

<!-- Any additional information for reviewers -->

## 🔗 Related Links

<!-- Links to relevant documentation, designs, etc. -->
- Design: 
- Documentation: 
- Related PRs: 

---

### For Reviewers

#### Review Checklist
- [ ] Code quality and style
- [ ] Functionality works as expected
- [ ] Tests are comprehensive
- [ ] Documentation is adequate
- [ ] Security considerations addressed
- [ ] Performance impact acceptable
- [ ] No obvious bugs or edge cases missed

#### Approval Criteria
- [ ] All automated checks pass
- [ ] Manual testing completed
- [ ] Code review completed
- [ ] Security review completed (if applicable)
- [ ] Performance review completed (if applicable)
