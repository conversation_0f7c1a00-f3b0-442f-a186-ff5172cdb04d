name: PR Review Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened, ready_for_review]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Basic validation and setup
  validate:
    name: Validate PR
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    outputs:
      has-frontend-changes: ${{ steps.changes.outputs.frontend }}
      has-backend-changes: ${{ steps.changes.outputs.backend }}
      has-docs-changes: ${{ steps.changes.outputs.docs }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changes
        uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            frontend:
              - 'src/**'
              - 'public/**'
              - 'package.json'
              - 'vite.config.ts'
              - 'tsconfig.json'
            backend:
              - 'backend/**'
              - 'backend/package.json'
            docs:
              - 'docs/**'
              - '*.md'
              - '.github/**'

      - name: Check PR title format
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            chore
            ci
            build
          requireScope: false
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.

  # Code quality checks
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    needs: validate
    if: needs.validate.outputs.has-frontend-changes == 'true' || needs.validate.outputs.has-backend-changes == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint -- --format=@microsoft/eslint-formatter-sarif --output-file=eslint-results.sarif
        continue-on-error: true

      - name: Upload ESLint results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: eslint-results.sarif
          wait-for-processing: true

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Check for TODO/FIXME comments
        run: |
          echo "Checking for TODO/FIXME comments..."
          if grep -r "TODO\|FIXME" src/ backend/src/ --exclude-dir=node_modules; then
            echo "⚠️ Found TODO/FIXME comments. Please address them before merging."
            exit 1
          else
            echo "✅ No TODO/FIXME comments found."
          fi

  # Security checks
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: validate
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium
        continue-on-error: true

      - name: Check for hardcoded secrets
        run: |
          echo "Checking for potential secrets..."
          if grep -r "password\|secret\|key\|token" src/ backend/src/ --include="*.ts" --include="*.tsx" | grep -v "// " | grep -v "type\|interface\|import"; then
            echo "⚠️ Potential hardcoded secrets found. Please review."
            exit 1
          else
            echo "✅ No obvious hardcoded secrets found."
          fi

  # Testing
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: validate
    if: needs.validate.outputs.has-frontend-changes == 'true' || needs.validate.outputs.has-backend-changes == 'true'
    
    strategy:
      matrix:
        test-type: [unit, integration]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: npm run test:ci

      - name: Run integration tests
        if: matrix.test-type == 'integration'
        run: npm run test:integration || echo "Integration tests not configured"

      - name: Upload coverage reports
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage/lcov.info
          fail_ci_if_error: false

  # Build verification
  build:
    name: Build Check
    runs-on: ubuntu-latest
    needs: validate
    if: needs.validate.outputs.has-frontend-changes == 'true' || needs.validate.outputs.has-backend-changes == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build frontend
        if: needs.validate.outputs.has-frontend-changes == 'true'
        run: npm run build

      - name: Build backend
        if: needs.validate.outputs.has-backend-changes == 'true'
        run: cd backend && npm run build

      - name: Check bundle size
        if: needs.validate.outputs.has-frontend-changes == 'true'
        run: |
          echo "Checking bundle size..."
          du -sh dist/
          if [ $(du -s dist/ | cut -f1) -gt 10000 ]; then
            echo "⚠️ Bundle size is large. Consider optimization."
          else
            echo "✅ Bundle size is acceptable."
          fi

  # Performance and accessibility
  lighthouse:
    name: Lighthouse Audit
    runs-on: ubuntu-latest
    needs: [validate, build]
    if: needs.validate.outputs.has-frontend-changes == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli
          lhci autorun --upload.target=temporary-public-storage
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Database migration check
  database:
    name: Database Migration Check
    runs-on: ubuntu-latest
    needs: validate
    if: needs.validate.outputs.has-backend-changes == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install backend dependencies
        run: cd backend && npm ci

      - name: Check Prisma schema
        run: cd backend && npx prisma validate

      - name: Generate Prisma client
        run: cd backend && npx prisma generate

      - name: Check for migration conflicts
        run: |
          cd backend
          if [ -d "prisma/migrations" ]; then
            echo "Checking for migration conflicts..."
            # Add logic to check for conflicting migrations
            echo "✅ No migration conflicts detected."
          else
            echo "No migrations directory found."
          fi

  # Documentation check
  docs:
    name: Documentation Check
    runs-on: ubuntu-latest
    needs: validate
    if: needs.validate.outputs.has-docs-changes == 'true' || needs.validate.outputs.has-frontend-changes == 'true' || needs.validate.outputs.has-backend-changes == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check README updates
        run: |
          echo "Checking if README needs updates..."
          # Add logic to check if significant changes require README updates
          echo "✅ Documentation check passed."

      - name: Validate markdown
        run: |
          echo "Validating markdown files..."
          find . -name "*.md" -not -path "./node_modules/*" -not -path "./backend/node_modules/*" | while read file; do
            echo "Checking $file"
            # Add markdown linting if needed
          done
          echo "✅ Markdown validation passed."

  # Final status check
  pr-review-complete:
    name: PR Review Complete
    runs-on: ubuntu-latest
    needs: [validate, code-quality, security, test, build, lighthouse, database, docs]
    if: always()
    
    steps:
      - name: Check all jobs status
        run: |
          echo "Checking all job statuses..."
          if [[ "${{ needs.validate.result }}" == "success" && 
                "${{ needs.code-quality.result }}" != "failure" && 
                "${{ needs.security.result }}" != "failure" && 
                "${{ needs.test.result }}" != "failure" && 
                "${{ needs.build.result }}" != "failure" ]]; then
            echo "✅ All PR review checks passed!"
          else
            echo "❌ Some PR review checks failed."
            exit 1
          fi
