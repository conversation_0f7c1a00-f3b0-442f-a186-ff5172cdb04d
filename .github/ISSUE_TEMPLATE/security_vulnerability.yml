name: 🔒 Security Vulnerability
description: Report a security vulnerability (Private)
title: "[Security]: "
labels: ["type: security", "priority: critical", "status: needs-review"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        ⚠️ **IMPORTANT**: If this is a critical security vulnerability, please consider reporting it privately first.
        
        For critical vulnerabilities, please email: <EMAIL>
        
        This form should be used for non-critical security issues or after coordinated disclosure.

  - type: checkboxes
    id: disclosure
    attributes:
      label: Disclosure Agreement
      description: Please confirm your understanding
      options:
        - label: I understand this is a public issue and will be visible to everyone
          required: true
        - label: I have considered the impact and this is appropriate for public disclosure
          required: true
        - label: For critical vulnerabilities, I have contacted the security team privately first
          required: true

  - type: dropdown
    id: severity
    attributes:
      label: Severity Level
      description: How severe is this vulnerability?
      options:
        - Critical (Remote code execution, data breach)
        - High (Privilege escalation, authentication bypass)
        - Medium (Information disclosure, CSRF)
        - Low (Minor information leak, low-impact XSS)
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Vulnerability Category
      description: What type of vulnerability is this?
      options:
        - Authentication/Authorization
        - Input Validation
        - SQL Injection
        - Cross-Site Scripting (XSS)
        - Cross-Site Request Forgery (CSRF)
        - Insecure Direct Object Reference
        - Security Misconfiguration
        - Sensitive Data Exposure
        - Broken Access Control
        - Cryptographic Issues
        - Other
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Vulnerability Description
      description: Detailed description of the vulnerability
      placeholder: Describe the security vulnerability...
    validations:
      required: true

  - type: textarea
    id: impact
    attributes:
      label: Impact Assessment
      description: What is the potential impact of this vulnerability?
      placeholder: |
        - What data could be compromised?
        - What systems could be affected?
        - What actions could an attacker perform?
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Detailed steps to reproduce the vulnerability
      placeholder: |
        1. Step 1
        2. Step 2
        3. Step 3
        4. Observe vulnerability
    validations:
      required: true

  - type: textarea
    id: proof-of-concept
    attributes:
      label: Proof of Concept
      description: Provide a proof of concept (sanitized if necessary)
      placeholder: |
        Provide code, screenshots, or other evidence demonstrating the vulnerability.
        Please sanitize any sensitive information.

  - type: textarea
    id: affected-components
    attributes:
      label: Affected Components
      description: Which components/files are affected?
      placeholder: |
        - Component 1
        - File: /path/to/file.js
        - API endpoint: /api/endpoint
    validations:
      required: true

  - type: input
    id: affected-versions
    attributes:
      label: Affected Versions
      description: Which versions are affected?
      placeholder: e.g., v1.0.0 - v1.2.3, or "current main branch"

  - type: textarea
    id: mitigation
    attributes:
      label: Suggested Mitigation
      description: How can this vulnerability be fixed?
      placeholder: Describe potential fixes or mitigations...

  - type: checkboxes
    id: exploitation
    attributes:
      label: Exploitation Requirements
      description: What does an attacker need to exploit this?
      options:
        - label: No authentication required
        - label: Valid user account required
        - label: Admin/privileged account required
        - label: Physical access required
        - label: Social engineering required
        - label: Other prerequisites (describe below)

  - type: textarea
    id: prerequisites
    attributes:
      label: Prerequisites for Exploitation
      description: Any specific prerequisites for exploiting this vulnerability
      placeholder: Describe any prerequisites...

  - type: checkboxes
    id: attack-vector
    attributes:
      label: Attack Vector
      description: How can this vulnerability be exploited?
      options:
        - label: Remote network access
        - label: Local network access
        - label: Local access
        - label: Physical access
        - label: User interaction required

  - type: textarea
    id: references
    attributes:
      label: References
      description: Any relevant references (CVE, CWE, articles, etc.)
      placeholder: |
        - CWE-XXX: Description
        - CVE-XXXX-XXXX
        - https://example.com/reference

  - type: textarea
    id: additional
    attributes:
      label: Additional Information
      description: Any other relevant information
      placeholder: Any additional context or information...
