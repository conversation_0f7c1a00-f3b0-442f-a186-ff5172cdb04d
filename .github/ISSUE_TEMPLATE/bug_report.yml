name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["type: bug", "status: needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out this form as completely as possible.

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-submission Checklist
      description: Please verify these items before submitting
      options:
        - label: I have searched existing issues to ensure this bug hasn't been reported
          required: true
        - label: I have tested this on the latest version
          required: true
        - label: I have provided all required information below
          required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What you expected to happen
      placeholder: Describe what should happen...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened
      placeholder: Describe what actually happened...
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots/Videos
      description: If applicable, add screenshots or videos to help explain your problem
      placeholder: Drag and drop images/videos here or paste URLs...

  - type: dropdown
    id: severity
    attributes:
      label: Severity
      description: How severe is this bug?
      options:
        - Critical (System unusable, data loss)
        - High (Major functionality broken)
        - Medium (Some functionality affected)
        - Low (Minor issue, workaround available)
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which component is affected?
      options:
        - Authentication
        - Booking System
        - Dashboard
        - Payments
        - Video Calls
        - Messaging
        - Admin Panel
        - Database
        - API
        - UI/UX
        - Other
    validations:
      required: true

  - type: input
    id: browser
    attributes:
      label: Browser
      description: Which browser are you using?
      placeholder: e.g., Chrome 120.0, Firefox 121.0, Safari 17.0

  - type: input
    id: os
    attributes:
      label: Operating System
      description: Which OS are you using?
      placeholder: e.g., Windows 11, macOS 14.0, Ubuntu 22.04

  - type: input
    id: device
    attributes:
      label: Device
      description: What device are you using?
      placeholder: e.g., Desktop, iPhone 15, Samsung Galaxy S23

  - type: textarea
    id: console-logs
    attributes:
      label: Console Logs
      description: Any relevant console logs or error messages
      placeholder: Paste console logs here...
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Any other context about the problem
      placeholder: Add any other context about the problem here...

  - type: checkboxes
    id: impact
    attributes:
      label: Impact
      description: What is the impact of this bug?
      options:
        - label: Blocks user from completing critical tasks
        - label: Affects user experience but has workarounds
        - label: Minor inconvenience
        - label: Only affects edge cases

  - type: textarea
    id: workaround
    attributes:
      label: Workaround
      description: Is there a workaround for this issue?
      placeholder: Describe any workaround if available...
