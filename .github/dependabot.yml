version: 2
updates:
  # Frontend dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "lord-dubious"
    assignees:
      - "lord-dubious"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "type: dependencies"
      - "status: needs-review"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "typescript"
        update-types: ["version-update:semver-major"]
      - dependency-name: "vite"
        update-types: ["version-update:semver-major"]

  # Backend dependencies
  - package-ecosystem: "npm"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "10:00"
    open-pull-requests-limit: 10
    reviewers:
      - "lord-dubious"
    assignees:
      - "lord-dubious"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "type: dependencies"
      - "component: backend"
      - "status: needs-review"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "express"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@prisma/client"
        update-types: ["version-update:semver-major"]
      - dependency-name: "prisma"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "11:00"
    open-pull-requests-limit: 5
    reviewers:
      - "lord-dubious"
    assignees:
      - "lord-dubious"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "type: ci"
      - "status: needs-review"

  # Docker (if we add Docker later)
  # - package-ecosystem: "docker"
  #   directory: "/"
  #   schedule:
  #     interval: "weekly"
  #     day: "monday"
  #     time: "12:00"
  #   open-pull-requests-limit: 5
  #   reviewers:
  #     - "lord-dubious"
  #   assignees:
  #     - "lord-dubious"
  #   commit-message:
  #     prefix: "chore"
  #     include: "scope"
  #   labels:
  #     - "type: dependencies"
  #     - "component: docker"
  #     - "status: needs-review"
